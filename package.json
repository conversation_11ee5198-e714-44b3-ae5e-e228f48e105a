{"name": "billie-webm-converter", "version": "0.1.0", "description": "Billie WebM Converter - A simple and user-friendly online tool to convert videos to WebM format with 512x512 resizing", "scripts": {"dev": "node server.js", "start": "node server.js", "build": "echo 'No build step needed for Express server'", "lint": "echo 'No linting configured'"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "fluent-ffmpeg": "^2.1.2", "cors": "^2.8.5", "fs-extra": "^11.1.1"}, "devDependencies": {}}